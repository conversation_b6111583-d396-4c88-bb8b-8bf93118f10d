<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字化转型关键指标</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-red: #AE2105;
            --secondary-red: #C00000;
            --text-color: #242326;
            --background-light-gray: #F2F2F2;
            --background-white: #FFFFFF;
            --accent-blue: #A1D5FA;
            --accent-brown: #A87243;
        }

        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            width: 100%;
            font-family: 'Inter', sans-serif;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #e0e0e0;
        }

        .slide-container {
            width: 2667px;
            height: 1500px;
            background-color: var(--background-light-gray);
            color: var(--text-color);
            box-sizing: border-box;
            padding: 190px 540px 250px 200px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .slide-title {
            font-size: 90px;
            font-weight: 700;
            color: var(--primary-red);
            margin-bottom: 60px;
            line-height: 1.2;
            text-align: center;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            height: 100%;
        }

        .text-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .bullet-points {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .bullet-points li {
            font-size: 36px;
            font-weight: 400;
            color: var(--text-color);
            margin-bottom: 30px;
            line-height: 1.4;
            position: relative;
            padding-left: 50px;
        }

        .bullet-points li::before {
            content: '●';
            color: var(--primary-red);
            font-size: 40px;
            position: absolute;
            left: 0;
            top: -5px;
        }

        .chart-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: var(--background-white);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .chart-title {
            font-size: 42px;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 40px;
            text-align: center;
        }

        .chart-svg {
            width: 100%;
            height: 400px;
        }

        .chart-svg text {
            font-family: 'Inter', sans-serif;
            font-size: 24px;
            fill: var(--text-color);
        }

        .chart-svg .title-text {
            font-size: 28px;
            font-weight: 700;
        }

        .chart-svg .value-text {
            font-size: 20px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <h1 class="slide-title">2025年上半年数字化成果指标</h1>

        <div class="content-grid">
            <div class="text-content">
                <ul class="bullet-points">
                    <li>Boss3/Huoban3系统成功上线，业务流程全面数字化</li>
                    <li>历史文档处理：90129份文档抓取，5312份精准筛选</li>
                    <li>AI应用全面落地：4大核心场景应用成功</li>
                    <li>跨部门协作：AI共学群建立，数字化文化形成</li>
                </ul>
            </div>

            <div class="chart-container">
                <h3 class="chart-title">关键指标完成情况</h3>
                <svg class="chart-svg" viewBox="0 0 600 400">
                    <!-- 图表标题 -->
                    <text x="300" y="40" text-anchor="middle" class="title-text">各项目完成率对比</text>

                    <!-- X轴标签 -->
                    <text x="120" y="380" text-anchor="middle">业务系统</text>
                    <text x="240" y="380" text-anchor="middle">AI应用</text>
                    <text x="360" y="380" text-anchor="middle">数据处理</text>
                    <text x="480" y="380" text-anchor="middle">数字化共学</text>

                    <!-- Y轴标签 -->
                    <text x="40" y="200" text-anchor="middle" transform="rotate(-90 40 200)">完成率(%)</text>

                    <!-- 柱状图 -->
                    <!-- 业务系统 100% -->
                    <rect x="90" y="125" width="60" height="200" fill="#AE2105" rx="5" ry="5"></rect>
                    <text x="120" y="115" text-anchor="middle" class="value-text">80%</text>

                    <!-- AI应用 100% -->
                    <rect x="210" y="125" width="60" height="200" fill="#C00000" rx="5" ry="5"></rect>
                    <text x="240" y="115" text-anchor="middle" class="value-text">30%</text>

                    <!-- 数据处理 80% -->
                    <rect x="330" y="165" width="60" height="160" fill="#AE2105" rx="5" ry="5"></rect>
                    <text x="360" y="155" text-anchor="middle" class="value-text">30%</text>

                    <!-- 数字化共学 95% -->
                    <rect x="450" y="135" width="60" height="190" fill="#A1D5FA" rx="5" ry="5"></rect>
                    <text x="480" y="125" text-anchor="middle" class="value-text">95%</text>

                    <!-- 坐标轴 -->
                    <line x1="70" y1="325" x2="530" y2="325" stroke="#242326" stroke-width="2"></line>
                    <line x1="70" y1="325" x2="70" y2="80" stroke="#242326" stroke-width="2"></line>

                    <!-- Y轴刻度 -->
                    <line x1="65" y1="275" x2="75" y2="275" stroke="#242326" stroke-width="1"></line>
                    <text x="55" y="280" text-anchor="end" font-size="16">25</text>
                    
                    <line x1="65" y1="225" x2="75" y2="225" stroke="#242326" stroke-width="1"></line>
                    <text x="55" y="230" text-anchor="end" font-size="16">50</text>
                    
                    <line x1="65" y1="175" x2="75" y2="175" stroke="#242326" stroke-width="1"></line>
                    <text x="55" y="180" text-anchor="end" font-size="16">75</text>
                    
                    <line x1="65" y1="125" x2="75" y2="125" stroke="#242326" stroke-width="1"></line>
                    <text x="55" y="130" text-anchor="end" font-size="16">100</text>
                </svg>
            </div>
        </div>
    </div>
</body>
</html>
