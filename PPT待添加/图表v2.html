<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Content Slide</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
    <style>
        /*
         * Global styles for the body and HTML to ensure full coverage and proper font.
         * The body now uses flexbox to center the slide, and overflow is hidden.
         */
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            width: 100%;
            font-family: 'Inter', sans-serif; /* Using Inter font */
            overflow: hidden; /* Prevent scrolling, as the slide will scale to fit */
            display: flex; /* Use flexbox to center the slide */
            justify-content: center; /* Center horizontally */
            align-items: center; /* Center vertically */
            background-color: #F2F2F2; /* Background for the entire viewport area */
        }

        /*
         * Container for the entire slide, maintaining its original fixed dimensions.
         * This container will be scaled down by JavaScript to fit the viewport,
         * ensuring all internal elements maintain their relative positions and sizes.
         */
        .slide-container {
            width: 2667px; /* Original Canvas width */
            height: 1500px; /* Original Canvas height */
            background-color: #F2F2F2; /* Light gray background for the slide itself */
            color: #242326; /* Default text color for body */
            box-sizing: border-box; /* Include padding in the element's total width and height */
            /* Adjusted padding: 190px top, 200px right (was 540px), 250px bottom, 200px left */
            padding: 190px 200px 250px 200px;
            display: flex;
            /* Changed to row direction to allow text and graphics side-by-side */
            flex-direction: row;
            justify-content: space-between; /* Distribute space between text and graphics */
            align-items: flex-start; /* Align content to the top */
            transform-origin: 0 0; /* Scale from top-left corner */
            /* The 'transform' property will be set by JavaScript to scale the slide */
        }

        /*
         * Content area for text (title and bullet points).
         * Takes up approximately 55% of the available content width.
         */
        .text-content-area {
            display: flex;
            flex-direction: column;
            width: 55%; /* Allocate 55% width for text content */
            height: 100%;
            text-align: left;
            padding-right: 50px; /* Add some spacing between text and graphics */
            box-sizing: border-box;
        }

        /*
         * Styling for the main title of the slide.
         * Uses primary red color and bold typography.
         */
        .slide-title {
            font-size: 90px; /* Original font size */
            font-weight: 700; /* Bold typography */
            color: #AE2105; /* Primary red for headings */
            margin-bottom: 60px; /* Original margin */
            line-height: 1.2;
        }

        /*
         * Styling for bullet points and general body text.
         * Uses the specified text color and a readable font size.
         */
        .bullet-points {
            list-style: none; /* Remove default list style */
            padding: 0;
            margin: 0;
            font-size: 50px; /* Original font size */
            font-weight: 400; /* Regular weight for body text */
            color: #242326; /* Dark text color */
            line-height: 1.6; /* Spacing between lines for readability */
            /* margin-bottom removed as it's now part of the text-content-area flow */
        }

        .bullet-points li {
            margin-bottom: 25px; /* Original margin */
            position: relative;
            padding-left: 40px; /* Original padding */
        }

        .bullet-points li::before {
            content: '•'; /* Custom bullet point */
            color: #AE2105; /* Primary red for bullet */
            font-size: 1.2em; /* Slightly larger bullet */
            position: absolute;
            left: 0;
            top: 0;
        }

        /*
         * Container for graphics and data visualization.
         * Now specifically positioned on the right and takes remaining width.
         */
        .graphics-area {
            display: flex;
            flex-direction: column; /* Stack chart elements vertically */
            justify-content: center; /* Center chart vertically within its area */
            align-items: center; /* Center chart horizontally within its area */
            width: 45%; /* Allocate 45% width for graphics content */
            height: 100%;
            box-sizing: border-box;
            padding-left: 50px; /* Add some spacing between text and graphics */
        }

        /*
         * Styling for a simple bar chart example.
         * Uses SVG for scalable graphics and brand colors.
         * Adjusted size to fit the new column width.
         */
        .bar-chart-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 0; /* Adjusted margin as it's now centered in its column */
            width: 100%; /* Take full width of its parent (.graphics-area) */
            height: 100%; /* Take full height to allow vertical centering */
        }

        .bar-chart-container svg {
            width: 90%; /* Scale SVG to 90% of its container for internal padding */
            height: auto; /* Maintain aspect ratio */
            max-height: 100%; /* Ensure it doesn't overflow vertically */
            background-color: #FFFFFF; /* White background for the chart area */
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .bar-chart-container text {
            font-size: 30px; /* Original font size */
            fill: #242326;
            font-weight: 400;
        }
    </style>
</head>
<body>
    <div class="slide-container" id="slideContainer">
        <div class="text-content-area">
            <h1 class="slide-title">Key Insights and Strategic Focus</h1>

            <ul class="bullet-points">
                <li>[INSERT_CONTENT_HERE] - Comprehensive market analysis reveals significant growth opportunities in emerging sectors.</li>
                <li>[INSERT_CONTENT_HERE] - Our innovative product roadmap is aligned with customer needs and technological advancements.</li>
                <li>[INSERT_CONTENT_HERE] - Strategic partnerships are crucial for expanding our reach and enhancing service delivery.</li>
                <li>[INSERT_CONTENT_HERE] - Financial projections indicate strong profitability and sustainable long-term value creation.</li>
            </ul>
        </div>

        <div class="graphics-area">
            <!-- Example Data Visualization: Simple Bar Chart using SVG -->
            <div class="bar-chart-container">
                <svg viewBox="0 0 600 400">
                    <!-- Chart Title -->
                    <text x="300" y="50" text-anchor="middle" font-weight="bold">Quarterly Performance</text>

                    <!-- X-axis labels -->
                    <text x="150" y="380" text-anchor="middle">Q1</text>
                    <text x="300" y="380" text-anchor="middle">Q2</text>
                    <text x="450" y="380" text-anchor="middle">Q3</text>

                    <!-- Y-axis label -->
                    <text x="50" y="200" text-anchor="middle" transform="rotate(-90 50 200)">Revenue (M)</text>

                    <!-- Bars -->
                    <!-- Bar 1: Primary Red -->
                    <rect x="120" y="150" width="60" height="200" fill="#AE2105" rx="5" ry="5"></rect>
                    <text x="150" y="140" text-anchor="middle">$250</text>

                    <!-- Bar 2: Secondary Red (slightly different shade for contrast/highlight) -->
                    <rect x="270" y="100" width="60" height="250" fill="#C00000" rx="5" ry="5"></rect>
                    <text x="300" y="90" text-anchor="middle">$300</text>

                    <!-- Bar 3: Primary Red -->
                    <rect x="420" y="180" width="60" height="170" fill="#AE2105" rx="5" ry="5"></rect>
                    <text x="450" y="170" text-anchor="middle">$200</text>

                    <!-- Base line for bars -->
                    <line x1="100" y1="350" x2="500" y2="350" stroke="#242326" stroke-width="2"></line>
                    <line x1="100" y1="350" x2="100" y2="80" stroke="#242326" stroke-width="2"></line>
                </svg>
            </div>
        </div>
    </div>

    <script>
        // JavaScript to dynamically scale the slide to fit the viewport while maintaining aspect ratio
        function scaleSlide() {
            const slideContainer = document.getElementById('slideContainer');
            const originalWidth = 2667;
            const originalHeight = 1500;

            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            const widthRatio = viewportWidth / originalWidth;
            const heightRatio = viewportHeight / originalHeight;

            // Choose the smaller ratio to ensure the entire slide fits within the viewport
            const scale = Math.min(widthRatio, heightRatio);

            // Apply the scale transform to the slide container
            slideContainer.style.transform = `scale(${scale})`;
        }

        // Call scaleSlide on load and whenever the window is resized
        window.addEventListener('load', scaleSlide);
        window.addEventListener('resize', scaleSlide);
    </script>
</body>
</html>
