<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字化工作进展汇报</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-red: #AE2105;
            --secondary-red: #C00000;
            --text-color: #242326;
            --background-white: #FFFFFF;
            --background-light-gray: #F2F2F2;
            --accent-blue: #A1D5FA;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #e0e0e0;
        }

        .slide-canvas {
            width: 2667px;
            height: 1500px;
            background-color: var(--background-white);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 190px 540px 250px 200px;
            box-sizing: border-box;
            overflow: hidden;
            position: relative;
        }

        .main-title {
            font-size: 90px;
            font-weight: 700;
            color: var(--primary-red);
            text-align: center;
            margin-bottom: 80px;
            line-height: 1.2;
        }

        .progress-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            height: calc(100% - 200px);
        }

        .progress-section {
            background-color: var(--background-light-gray);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border-left: 8px solid var(--primary-red);
        }

        .section-title {
            font-size: 48px;
            font-weight: 700;
            color: var(--primary-red);
            margin-bottom: 30px;
            text-align: center;
        }

        .task-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            font-size: 32px;
            line-height: 1.4;
        }

        .task-bullet {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: var(--text-color);
            margin-right: 20px;
            flex-shrink: 0;
        }

        .completed-bullet {
            background-color: var(--primary-red);
        }

        .completed-task {
            color: var(--primary-red);
            font-weight: 600;
        }

        .progress-bar-container {
            width: 100%;
            background-color: #E0E0E0;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin-top: 20px;
            position: relative;
        }

        .progress-bar-fill {
            height: 100%;
            background-color: var(--primary-red);
            border-radius: 10px;
            transition: width 0.5s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 15px;
            box-sizing: border-box;
        }

        .progress-percentage {
            color: #FFFFFF;
            font-weight: 700;
            font-size: 16px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="slide-canvas">
        <h1 class="main-title">2025年上半年数字化工作进展</h1>
        
        <div class="progress-grid">
            <div class="progress-section">
                <h2 class="section-title">业务系统开发</h2>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成联劝业务数据字典</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成业务联调测试（80项功能）</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成第一轮开发调整（75条）</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">Boss3/Huoban3系统上线</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar-fill" style="width: 100%;">
                        <span class="progress-percentage">100%</span>
                    </div>
                </div>
            </div>

            <div class="progress-section">
                <h2 class="section-title">历史数据处理</h2>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">抓取90129份历史文档</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">筛选出5312份相关文档</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成10年+历史业务数据迁移</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet"></span>
                    <span>数据质量提升与标准化</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar-fill" style="width: 80%;">
                        <span class="progress-percentage">80%</span>
                    </div>
                </div>
            </div>

            <div class="progress-section">
                <h2 class="section-title">AI应用探索</h2>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">项目评审AI化</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">捐赠人答谢自动化</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">历史资料数据提取</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">新版官网demo生成</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar-fill" style="width: 100%;">
                        <span class="progress-percentage">100%</span>
                    </div>
                </div>
            </div>

            <div class="progress-section">
                <h2 class="section-title">数字化共学</h2>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">低代码+AI应用模板分享</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">组建跨部门AI共学群</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">共学最新AI工具及应用案例</span>
                </div>
                <div class="task-item">
                    <span class="task-bullet"></span>
                    <span>持续优化共学机制</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar-fill" style="width: 85%;">
                        <span class="progress-percentage">85%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>