<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下半年工作计划</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-red: #AE2105;
            --secondary-red: #C00000;
            --text-color: #242326;
            --background-white: #FFFFFF;
            --background-light-gray: #F2F2F2;
            --accent-blue: #A1D5FA;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #e0e0e0;
        }

        .slide-canvas {
            width: 2667px;
            height: 1500px;
            background: linear-gradient(to bottom, var(--background-white), var(--background-light-gray));
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 190px 540px 250px 200px;
            box-sizing: border-box;
            overflow: hidden;
            position: relative;
        }

        .main-title {
            font-size: 90px;
            font-weight: 700;
            color: var(--primary-red);
            text-align: center;
            margin-bottom: 80px;
            line-height: 1.2;
        }

        .timeline-container {
            position: relative;
            width: 100%;
            height: calc(100% - 200px);
        }

        .timeline-line {
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background-color: var(--primary-red);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 120px;
            display: flex;
            align-items: center;
        }

        .timeline-item:nth-child(odd) {
            justify-content: flex-start;
        }

        .timeline-item:nth-child(even) {
            justify-content: flex-end;
        }

        .timeline-content {
            background-color: var(--background-white);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border: 3px solid var(--primary-red);
            width: 45%;
            position: relative;
        }

        .timeline-item:nth-child(odd) .timeline-content {
            margin-right: auto;
            margin-left: 0;
        }

        .timeline-item:nth-child(even) .timeline-content {
            margin-left: auto;
            margin-right: 0;
        }

        .timeline-marker {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 30px;
            height: 30px;
            background-color: var(--primary-red);
            border-radius: 50%;
            border: 6px solid var(--background-white);
            z-index: 10;
        }

        .quarter-title {
            font-size: 36px;
            font-weight: 700;
            color: var(--primary-red);
            margin-bottom: 20px;
        }

        .task-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .task-item {
            font-size: 28px;
            color: var(--text-color);
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
            line-height: 1.4;
        }

        .task-item::before {
            content: '▶';
            color: var(--primary-red);
            position: absolute;
            left: 0;
            top: 0;
        }

        .priority-high {
            color: var(--primary-red);
            font-weight: 600;
        }

        .priority-medium {
            color: var(--secondary-red);
            font-weight: 500;
        }

        .priority-support {
            color: var(--accent-blue);
            font-weight: 400;
        }
    </style>
</head>
<body>
    <div class="slide-canvas">
        <h1 class="main-title">2025年下半年工作重点规划</h1>
        
        <div class="timeline-container">
            <div class="timeline-line"></div>
            
            <div class="timeline-item">
                <div class="timeline-content">
                    <h3 class="quarter-title">Q3 2025 (7-9月)</h3>
                    <ul class="task-list">
                        <li class="task-item priority-high">业务系统一期收尾（7.16-9.16）</li>
                        <li class="task-item priority-high">历史文件数据提取（7.30-8.30）</li>
                        <li class="task-item priority-medium">数字化战略制定项目启动（8.1）</li>
                        <li class="task-item priority-support">知识库运营优化启动</li>
                    </ul>
                </div>
                <div class="timeline-marker"></div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <h3 class="quarter-title">Q4 2025 (10-12月)</h3>
                    <ul class="task-list">
                        <li class="task-item priority-high">数字化战略制定项目完成（至10.30）</li>
                        <li class="task-item priority-high">业务系统二期开发启动（9.1）</li>
                        <li class="task-item priority-medium">知识库运营优化持续推进</li>
                        <li class="task-item priority-support">AI Agent和提示词体系建设</li>
                    </ul>
                </div>
                <div class="timeline-marker"></div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <h3 class="quarter-title">2026年展望</h3>
                    <ul class="task-list">
                        <li class="task-item priority-high">业务系统二期完成（至1.30）</li>
                        <li class="task-item priority-medium">联劝官网优化（AI推荐、成就查询）</li>
                        <li class="task-item priority-medium">数据仓库开发</li>
                        <li class="task-item priority-support">数字化基金会转型完成</li>
                    </ul>
                </div>
                <div class="timeline-marker"></div>
            </div>
        </div>
    </div>
</body>
</html>
