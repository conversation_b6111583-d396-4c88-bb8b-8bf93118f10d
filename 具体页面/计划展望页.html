<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下半年工作计划</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-red: #AE2105;
            --secondary-red: #C00000;
            --text-color: #242326;
            --background-white: #FFFFFF;
            --background-light-gray: #F2F2F2;
            --accent-blue: #A1D5FA;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #e0e0e0;
        }

        .slide-canvas {
            width: 2667px;
            height: 1500px;
            background: linear-gradient(to bottom, var(--background-white), var(--background-light-gray));
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 190px 540px 250px 200px;
            box-sizing: border-box;
            overflow: hidden;
            position: relative;
        }

        .main-title {
            font-size: 90px;
            font-weight: 700;
            color: var(--primary-red);
            text-align: center;
            margin-bottom: 80px;
            line-height: 1.2;
        }

        .timeline-container {
            position: relative;
            width: 100%;
            height: calc(100% - 200px);
        }

        .timeline-line {
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background-color: var(--primary-red);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 120px;
            display: flex;
            align-items: center;
        }

        .timeline-item:nth-child(odd) {
            justify-content: flex-start;
        }

        .timeline-item:nth-child(even) {
            justify-content: flex-end;
        }

        .timeline-content {
            background-color: var(--background-white);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border: 3px solid var(--primary-red);
            width: 45%;
            position: relative;
        }

        .timeline-item:nth-child(odd) .timeline-content {
            margin-right: auto;
            margin-left: 0;
        }

        .timeline-item:nth-child(even) .timeline-content {
            margin-left: auto;
            margin-right: 0;
        }

        .timeline-marker {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 30px;
            height: 30px;
            background-color: var(--primary-red);
            border-radius: 50%;
            border: 6px solid var(--background-white);
            z-index: 10;
        }

        .quarter-title {
            font-size: 36px;
            font-weight: 700;
            color: var(--primary-red);
            margin-bottom: 20px;
        }

        .task-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .task-item {
            font-size: 28px;
            color: var(--text-color);
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
            line-height: 1.4;
        }

        .task-item::before {
            content: '▶';
            color: var(--primary-red);
            position: absolute;
            left: 0;
            top: 0;
        }

        .priority-high {
            color: var(--primary-red);
            font-weight: 600;
        }

        .priority-medium {
            color: var(--secondary-red);
            font-weight: 500;
        }

        .priority-support {
            color: var(--accent-blue);
            font-weight: 400;
        }
    </style>
</head>
<body>
    <div class="slide-canvas">
        <h1 class="main-title">下半年工作重点规划</h1>
        
        <div class="timeline-container">
            <div class="timeline-line"></div>
            
            <div class="timeline-item">
                <div class="timeline-content">
                    <h3 class="quarter-title">Q3 2024 (7-9月)</h3>
                    <ul class="task-list">
                        <li class="task-item priority-high">移动端应用正式上线</li>
                        <li class="task-item priority-high">核心系统性能优化</li>
                        <li class="task-item priority-medium">数据分析平台部署</li>
                        <li class="task-item priority-support">用户体验优化</li>
                    </ul>
                </div>
                <div class="timeline-marker"></div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <h3 class="quarter-title">Q4 2024 (10-12月)</h3>
                    <ul class="task-list">
                        <li class="task-item priority-high">智能决策支持系统</li>
                        <li class="task-item priority-high">全流程自动化实现</li>
                        <li class="task-item priority-medium">安全体系升级</li>
                        <li class="task-item priority-support">年度效果评估</li>
                    </ul>
                </div>
                <div class="timeline-marker"></div>
            </div>

            <!-- <div class="timeline-item">
                <div class="timeline-content">
                    <h3 class="quarter-title">2025年展望</h3>
                    <ul class="task-list">
                        <li class="task-item priority-high">AI技术深度应用</li>
                        <li class="task-item priority-medium">生态系统建设</li>
                        <li class="task-item priority-medium">数字化文化建设</li>
                        <li class="task-item priority-support">持续创新机制</li>
                    </ul>
                </div>
                <div class="timeline-marker"></div>
            </div> -->
        </div>
    </div>
</body>
</html>
