<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Transformation Progress Report</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            /* 符合联劝公益AI绘图提示词模板标准 */
            width: 2667px;
            height: 1500px;
            background-color: #FFFFFF; /* White background */
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
            /* 标准边距: 上190px, 下250px, 左200px, 右540px */
            padding: 190px 540px 250px 200px;
            box-sizing: border-box;
            overflow: hidden;
            position: relative;
        }

        /* Custom scrollbar for better aesthetics, if any overflow happens */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Styles for content sections */
        .progress-section {
            background-color: #F2F2F2; /* 符合联劝公益标准的背景色 */
            border-radius: 1rem; /* 适度圆角 */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* 轻微阴影 */
            padding: 2rem; /* 适中的内边距 */
            margin-bottom: 1.5rem; /* 卡片间距 */
            border: 1px solid rgba(0, 0, 0, 0.03); /* 细边框 */
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            color: #242326; /* 联劝公益标准文字色 */
            height: auto; /* 自适应高度 */
        }

        .progress-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
        }

        .section-title {
            font-size: 1.8rem; /* 适中的标题字号 */
            font-weight: 700;
            color: #AE2105; /* 使用联劝公益品牌红色 */
            margin-bottom: 1.5rem;
            text-align: center;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #AE2105; /* 品牌红色分割线 */
        }

        .task-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 0.8rem;
            font-size: 1rem; /* 适中的任务字号 */
            line-height: 1.4;
        }

        .completed-task {
            color: #AE2105; /* Brand red for completed items */
            font-weight: 600;
        }

        .task-bullet {
            width: 1.2rem;
            height: 1.2rem;
            border-radius: 50%;
            background-color: #242326; /* Default bullet color */
            margin-right: 1rem;
            flex-shrink: 0;
            margin-top: 0.35rem; /* Align with text */
        }

        .completed-bullet {
            background-color: #AE2105; /* Red for completed bullets */
        }

        .progress-bar-container {
            width: 100%;
            background-color: #E0E0E0; /* 浅灰色轨道 */
            border-radius: 0.5rem;
            height: 1.2rem; /* 适中的进度条高度 */
            overflow: hidden;
            margin-top: 1rem;
            position: relative;
        }

        .progress-bar-fill {
            height: 100%;
            background-color: #AE2105; /* 联劝公益品牌红色填充 */
            border-radius: 0.5rem;
            transition: width 0.5s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 0.8rem;
            box-sizing: border-box;
        }

        .progress-percentage {
            color: #FFFFFF;
            font-weight: 700;
            font-size: 1rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        /* 内容安全区域容器 */
        .content-container {
            width: 1927px; /* 内容安全区域宽度 */
            height: 1060px; /* 内容安全区域高度 */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
        }
    </style>
</head>
<body class="text-gray-800">
    <!-- 内容安全区域容器 -->
    <div class="content-container">
        <!-- Title Section -->
        <div class="mb-12 text-center" style="margin-top: 2rem; margin-bottom: 3rem;">
            <h1 class="text-6xl font-bold" style="color: #AE2105; line-height: 1.2;">数字化工作上半年进展</h1>
            <p class="text-2xl text-gray-600 mt-3">Digital Transformation Progress Report (H1)</p>
        </div>

        <!-- Main Content Area - Progress Sections -->
        <div class="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Work Stream 1: Core System Modernization -->
        <div class="progress-section">
            <h2 class="section-title">核心系统现代化</h2>
            <ul class="list-none p-0">
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成新系统需求分析与方案选定</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成供应商合同签署与团队组建</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>启动系统集成与数据迁移规划</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>完成核心模块开发 (20%)</span>
                </li>
            </ul>
            <div class="progress-bar-container">
                <div class="progress-bar-fill" style="width: 75%;">
                    <span class="progress-percentage">75%</span>
                </div>
            </div>
        </div>

        <!-- Work Stream 2: Data Foundation & Intelligence -->
        <div class="progress-section">
            <h2 class="section-title">数据基础与智能</h2>
            <ul class="list-none p-0">
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成数据平台架构设计</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成关键数据源集成 (80%)</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>启动数据治理框架建设</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>完成首批BI报表开发</span>
                </li>
            </ul>
            <div class="progress-bar-container">
                <div class="progress-bar-fill" style="width: 60%;">
                    <span class="progress-percentage">60%</span>
                </div>
            </div>
        </div>

        <!-- Work Stream 3: Digital Talent & Culture -->
        <div class="progress-section">
            <h2 class="section-title">数字化人才与文化</h2>
            <ul class="list-none p-0">
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成数字化能力评估与差距分析</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet completed-bullet"></span>
                    <span class="completed-task">完成首期内部培训课程开发与交付</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>启动跨部门数字化创新工作坊</span>
                </li>
                <li class="task-item">
                    <span class="task-bullet"></span>
                    <span>制定年度数字化文化推广计划</span>
                </li>
            </ul>
            <div class="progress-bar-container">
                <div class="progress-bar-fill" style="width: 80%;">
                    <span class="progress-percentage">80%</span>
                </div>
            </div>
        </div>
    </div> <!-- 关闭内容安全区域容器 -->
</body>
</html>
