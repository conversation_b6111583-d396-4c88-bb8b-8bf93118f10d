<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字化工作进展汇报 - 封面页</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-red: #AE2105;
            --secondary-red: #C00000;
            --warm-red: #B74135;
            --text-color: #242326;
            --background-white: #FFFFFF;
            --background-light-gray: #F2F2F2;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #e0e0e0;
        }

        .slide-canvas {
            width: 2667px;
            height: 1500px;
            background-color: var(--background-white);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        .content-area {
            padding: 190px 540px 250px 200px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            z-index: 10;
        }

        .title {
            font-size: 120px;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 40px;
            line-height: 1.1;
        }

        .subtitle {
            font-size: 60px;
            font-weight: 600;
            color: var(--primary-red);
            margin-bottom: 60px;
            line-height: 1.2;
        }

        .date {
            font-size: 36px;
            font-weight: 400;
            color: var(--text-color);
            position: absolute;
            bottom: 250px;
            left: 50%;
            transform: translateX(-50%);
        }

        .geometric-accent {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, var(--primary-red), var(--warm-red));
            z-index: 5;
            border-top-left-radius: 50px;
            border-top-right-radius: 50px;
        }

        .geometric-accent::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 50%;
            background-color: var(--secondary-red);
            opacity: 0.3;
            border-top-left-radius: 50px;
            border-top-right-radius: 50px;
        }
    </style>
</head>
<body>
    <div class="slide-canvas">
        <div class="content-area">
            <h1 class="title">数字化工作进展汇报</h1>
            <h2 class="subtitle">2024年上半年总结与下半年规划</h2>
            <span class="date">2024年7月</span>
        </div>
        <div class="geometric-accent"></div>
    </div>
</body>
</html>
