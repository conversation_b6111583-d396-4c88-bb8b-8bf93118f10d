<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字化工作进展 - 内容页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-red: #AE2105;
            --secondary-red: #C00000;
            --text-color: #242326;
            --background-light-gray: #F2F2F2;
            --background-white: #FFFFFF;
            --accent-blue: #A1D5FA;
            --accent-brown: #A87243;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #e0e0e0;
        }

        .slide-canvas {
            width: 2667px;
            height: 1500px;
            background-color: var(--background-light-gray);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        .content-area {
            padding: 190px 540px 250px 200px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .heading {
            font-size: 90px;
            font-weight: 700;
            color: var(--primary-red);
            margin-bottom: 60px;
            line-height: 1.2;
        }

        .bullet-point {
            font-size: 48px;
            font-weight: 400;
            color: var(--text-color);
            margin-bottom: 40px;
            line-height: 1.4;
            position: relative;
            padding-left: 60px;
        }

        .bullet-point::before {
            content: '•';
            color: var(--primary-red);
            font-size: 60px;
            position: absolute;
            left: 0;
            top: -10px;
        }

        .data-visualization {
            position: absolute;
            right: 200px;
            top: 300px;
            width: 400px;
            height: 600px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            padding: 20px;
            background-color: var(--background-white);
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .chart-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 20px;
        }

        .bar-chart-container {
            display: flex;
            align-items: flex-end;
            height: 100%;
            width: 100%;
            gap: 15px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--text-color);
        }

        .bar {
            flex: 1;
            background-color: var(--primary-red);
            border-radius: 5px 5px 0 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            position: relative;
        }

        .bar:nth-child(even) {
            background-color: var(--secondary-red);
        }

        .bar-value {
            position: absolute;
            bottom: -40px;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-color);
        }

        .bar-label {
            position: absolute;
            top: -30px;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <div class="slide-canvas">
        <div class="content-area">
            <h1 class="heading">2025年上半年数字化成果</h1>
            <ul style="list-style: none; padding: 0;">
                <li class="bullet-point">数字化共学：建立跨部门AI共学群，分享低代码+AI应用模板</li>
                <li class="bullet-point">业务系统开发：Boss3/Huoban3系统成功上线，完成业务数据字典</li>
                <li class="bullet-point">AI应用探索：项目评审、捐赠人答谢、历史资料数据提取全面应用</li>
                <li class="bullet-point">数据治理：抓取90129份文档，筛选出5312份相关文档</li>
            </ul>
        </div>

        <div class="data-visualization">
            <h3 class="chart-title">项目进展统计</h3>
            <div class="bar-chart-container">
                <div class="bar" style="height: 100%;">
                    <span class="bar-label">100%</span>
                    <span class="bar-value">系统上线</span>
                </div>
                <div class="bar" style="height: 75%;">
                    <span class="bar-label">75</span>
                    <span class="bar-value">需求调整</span>
                </div>
                <div class="bar" style="height: 90%;">
                    <span class="bar-label">5312</span>
                    <span class="bar-value">文档筛选</span>
                </div>
                <div class="bar" style="height: 85%;">
                    <span class="bar-label">4</span>
                    <span class="bar-value">AI应用</span>
                </div>
            </div>
            <p style="font-size: 20px; color: #666; margin-top: 20px; text-align: center;">*展示关键项目完成情况</p>
        </div>
    </div>
</body>
</html>
