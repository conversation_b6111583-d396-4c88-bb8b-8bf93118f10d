<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Summary Slide</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@700&display=swap" rel="stylesheet">
    <style>
        /*
         * Global styles for the body and HTML to ensure full coverage and proper font.
         */
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            width: 100%;
            font-family: 'Inter', sans-serif; /* Using Inter font as specified */
            overflow: hidden; /* Prevent scrolling */
        }

        /*
         * Container for the entire slide, setting the dimensions and background gradient.
         * The background uses a linear gradient from brand red to a slightly lighter red.
         */
        .slide-container {
            width: 2667px; /* Canvas width */
            height: 1500px; /* Canvas height */
            background: linear-gradient(to right, #AE2105, #B74135); /* Brand red gradient */
            color: #FFFFFF; /* White text color for contrast */
            display: flex;
            justify-content: center; /* Center content horizontally */
            align-items: center; /* Center content vertically */
            box-sizing: border-box; /* Include padding in the element's total width and height */
            padding: 190px 540px 250px 200px; /* Top, Right, Bottom, Left margins */
        }

        /*
         * Content area within the slide, defining the layout for the summary points.
         * It uses flexbox for vertical arrangement of items.
         */
        .content-area {
            display: flex;
            flex-direction: column; /* Stack summary points vertically */
            justify-content: center; /* Center content vertically within its area */
            align-items: flex-start; /* Align text to the start (left) within its area */
            width: 100%; /* Take full width of the padded container */
            height: 100%; /* Take full height of the padded container */
            text-align: left; /* Ensure text alignment is left */
        }

        /*
         * Styling for individual summary points.
         * Uses bold typography and a larger font size for impact.
         */
        .summary-point {
            font-size: 80px; /* Large font size for impact */
            font-weight: 700; /* Bold typography */
            line-height: 1.4; /* Spacing between lines for readability */
            margin-bottom: 40px; /* Space between summary points */
            white-space: pre-wrap; /* Preserve whitespace and allow wrapping */
        }

        /*
         * Remove bottom margin for the last summary point to avoid extra space.
         */
        .summary-point:last-child {
            margin-bottom: 0;
        }

        /*
         * Responsive adjustments for smaller screens, though the primary design is for a fixed canvas.
         * This ensures it scales down gracefully if viewed in a browser.
         */
        @media (max-width: 2667px) {
            .slide-container {
                width: 100vw; /* Use viewport width */
                height: 100vh; /* Use viewport height */
                padding: 7vw 20vw 9vw 7.5vw; /* Scale padding proportionally */
                background-size: cover; /* Ensure background covers the area */
            }
            .summary-point {
                font-size: 3vw; /* Scale font size proportionally */
                margin-bottom: 1.5vw; /* Scale margin proportionally */
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-area">
            <!-- Placeholder for your summary points. Replace with your actual content. -->
            <div class="summary-point">[您的总结要点一]</div>
            <div class="summary-point">[您的总结要点二]</div>
            <div class="summary-point">[您的总结要点三]</div>
            <div class="summary-point">[您的总结要点四]</div>
        </div>
    </div>
</body>
</html>
