
import subprocess
import time
import os

def take_screenshot(html_file, output_file):
    # 获取绝对路径
    html_path = os.path.abspath(html_file)
    output_path = os.path.abspath(output_file)
    
    # 使用Firefox headless模式截图
    cmd = [
        'firefox',
        '--headless',
        '--screenshot=' + output_path,
        '--window-size=2667,1500',
        'file://' + html_path
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f'Successfully created screenshot: {output_file}')
            return True
        else:
            print(f'Error creating screenshot: {result.stderr}')
            return False
    except subprocess.TimeoutExpired:
        print(f'Timeout creating screenshot for {html_file}')
        return False
    except Exception as e:
        print(f'Exception creating screenshot: {e}')
        return False

# 要截图的HTML文件列表
html_files = [
    'PPT待添加/封面.html',
    'PPT待添加/内容页面.html', 
    'PPT待添加/进展汇报页.html',
    'PPT待添加/计划展望页.html',
    'PPT待添加/总结页面.html',
    'PPT待添加/图表.html'
]

# 为每个HTML文件生成截图
for html_file in html_files:
    if os.path.exists(html_file):
        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(html_file))[0]
        output_file = f'PPT待添加/{base_name}.png'
        
        print(f'Processing {html_file}...')
        success = take_screenshot(html_file, output_file)
        
        if success:
            print(f'✓ {base_name}.png created')
        else:
            print(f'✗ Failed to create {base_name}.png')
        
        # 等待一下避免过快
        time.sleep(2)
    else:
        print(f'File not found: {html_file}')

print('Screenshot generation completed!')
